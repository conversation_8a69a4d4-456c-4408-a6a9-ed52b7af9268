<template lang="pug">
.bar-header
  .logo
    .logo-holder
      img(:src="logo")
    p {{getHeaderName()}}
  .right
    .select-btn-group
      .system-language(v-if="languageMenuEnable")
        el-dropdown(trigger="click" placement="bottom-start" :popper-class="'lang-popper'" @command="handleLanguageChange")
          el-button.current-lang(link size="small")
            span.text {{languageAlias}}
            el-icon
              i-ep-arrow-down
          template(#dropdown)
            el-dropdown-menu.dropdown-list
              template(v-for="item in langList")
                el-dropdown-item(:command="item" :disabled="languageStore.lang === item.value")
                  span.language-item {{item.label}}
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { setLocale } from '@/i18n'
import { useTheme } from '@/composables/useTheme'
import { useLanguageStore } from '@/stores/language'
import { useCommonStore } from '@/stores/common.ts'
import type { AvailableLocales } from '@/types/i18n.types'
import type { Lang } from '@/types/lang'
import { language as languageConfig } from '@/conf'

const { t, locale } = useI18n()
const router = useRouter()
const route = useRoute()
const { currentTheme, toggleTheme } = useTheme()
const languageStore = useLanguageStore()
const commonStore = useCommonStore()
const languageMenuEnable = ref(true)
// commonStore data
const logo = computed(() => adminInfo.value.logoFilePath || commonStore.sysLogo)
const userInfo = computed(() => commonStore.userBaseInfo)
const adminInfo = computed(() => commonStore.adminInfo)
// Mobile menu status
const mobileMenuOpen = ref(false)
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

// Dark Mode
const isDarkMode = computed(() => currentTheme.value === 'dark')

// lang List
const langList = languageStore.langPackage
const languageAlias = computed(() => languageConfig.alias[locale.value])
// Language Change
const handleLanguageChange = (item: Lang) => {
  setLocale(item.value as AvailableLocales)
}

onMounted(() => {
})

onUnmounted(() => {
})

// Methods
function getHeaderName () {
  console.log(adminInfo.value, userInfo.value)
  if (adminInfo.value.roleType === '10' || adminInfo.value.roleType === 10) {
    return userInfo.value.userName
  }
  return adminInfo.value.companyName
}

</script>

<style lang="scss">
.lang-popper {
  max-width: 120px;
}
</style>
<style lang="scss" scoped>
@use "./index.scss" as *;
</style>
