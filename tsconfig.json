// tsconfig.json
{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "bundler",     // Recommended for Vite - bundler
    "composite": true,                 // Support build cache
    "esModuleInterop": true,           // Support for default imports from CommonJS modules
    "allowSyntheticDefaultImports": true, // Allow synthetic default imports
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@res/*": ["./public/res/*"],
      "@data/*": ["./public/data/*"]
    },
    "types": ["vite/client", "element-plus/global", "vue"]
  },
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue" ,"vite.config.*", "plugins/**/*.ts"],
  "exclude": ["src/**/__tests__/*"]
}
