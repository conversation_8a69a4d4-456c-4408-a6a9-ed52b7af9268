'use strict'

// Print the execution time of hook functions
export const hookTime = (component: string, hookFnName: string): void => {
  const date = new Date()
  const m = date.getMinutes()
  const s = date.getSeconds()
  const ms = date.getMilliseconds()
  console.log(`${component} ${hookFnName} ${m}-${s}-${ms}`)
}

// Convert flat format data to deep recursive structure
interface TreeItem {
  id: any
  parent_id: any
  lev?: number
  [key: string]: any
}

export const sonsTree = (arr: TreeItem[], id: any): TreeItem[] => {
  const temp: TreeItem[] = []
  const lev = 0
  const forFn = function (arr: TreeItem[], id: any, lev: number): void {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      if (item.parent_id === id) {
        item.lev = lev
        temp.push(item)
        forFn(arr, item.id, lev + 1)
      }
    }
  }
  forFn(arr, id, lev)
  return temp
}

// Recursive function to build tree structure
interface TreeNode {
  id: any
  pid?: any
  children?: TreeNode[]
  [key: string]: any
}

export const toTree = (data: TreeNode[]): TreeNode[] => {
  // Delete all children properties to prevent multiple calls
  data.forEach(function (item: TreeNode) {
    delete item.children
  })
  // Store data as a map indexed by id
  const map: Record<string, TreeNode> = {}
  data.forEach(function (item: TreeNode) {
    map[item.id] = item
  })
  // console.log(map);
  const val: TreeNode[] = []
  data.forEach(function (item: TreeNode) {
    // Find parent by current item's pid in the map
    const parent = map[item.pid]
    // If parent found, add current item to parent's children
    if (parent) {
      (parent.children || (parent.children = [])).push(item)
    } else {
      // If no parent found, add current item to root level
      val.push(item)
    }
  })
  return val
}

// Get URL parameter by name
export const getUrlAttr = (_attr: string): string | null => {
  const reg = new RegExp('(^|&)' + _attr + '=([^&]*)(&|$)', 'i')
  const r = window.location.search.substr(1).match(reg)
  if (r != null) return unescape(r[2])
  return null
}

// Concatenate all values in an object
export const getFilterDataString = (data: Record<string, any>): string => {
  let str = ''
  for (const key in data) {
    if (data.hasOwnProperty(key) && data[key]) {
      str += ' ' + data[key]
    }
  }
  return str
}

export const staticUrl = import.meta.env.BASE_URL

// Set the focus and move the cursor to the end of the text
export function getSelectPos (targetID: string): void {
  const esrc = document.getElementById(targetID)
  if (!esrc) return
  const range = document.createRange()
  const sel = window.getSelection()
  if (!sel) return
  range.selectNodeContents(esrc)
  range.collapse(false)
  sel.removeAllRanges()
  sel.addRange(range)
}

// verify Text string length
interface VerifyResult {
  exceed: boolean
  count: number
  cont: string
}

export function verifyCharacterLength (str: string, maxNoteLength?: number): VerifyResult {
  str = str || ''
  str = str.replace(/\r\n/, '')
  let bytesCount = 0, endIndex = 0, exceed = false

  // If maxNoteLength is not provided, set a default value or skip length check
  const maxLength = maxNoteLength ?? Number.MAX_SAFE_INTEGER

  for (let i = 0, n = str.length; i < n; i++) {
    const bytes = encodeURIComponent(str[i]).replace(/%[A-F\d]{2}/g, 'U').length
    if ((bytesCount + bytes) > maxLength) {
      exceed = true
      break
    } else {
      bytesCount = bytesCount + bytes
    }
    endIndex = i
  }
  return {
    exceed: exceed,
    count: bytesCount,
    cont: str.substring(0, endIndex + 1)
  }
}

export function uuid2 (): string {
  const s: string[] = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((Number(s[19]) & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-'

  const uuid = s.join('')
  return uuid
}

export function uuid (len = 16, radix = 16): string {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
  const uuid: string[] = []
  let i: number
  radix = radix || chars.length

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix]
  } else {
    // rfc4122, version 4 form
    let r: number

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
    uuid[14] = '4'

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | Math.random() * 16
        uuid[i] = chars[(i === 19) ? (r & 0x3) | 0x8 : r]
      }
    }
  }

  return uuid.join('')
}

export function resizeObserver (callback: (entries: ResizeObserverEntry[]) => void): ResizeObserver {
  const resizeObserver = new ResizeObserver(entries => {
    callback(entries)
  })
  return resizeObserver
}
