import '@/assets/scss/main.scss'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createHead } from '@vueuse/head'

import App from './App.vue'
import router from './router'
import { i18n } from './i18n'
// import { useUserStore } from './stores/user'
import { useTheme } from './composables/useTheme'
import { setupIcons } from '@/plugins/icons'
import fontawesome from './plugins/fontawesome'
import { setupDirectives } from '@/directives'

// AOS动画库
import AOS from 'aos'
import 'aos/dist/aos.css'

// Import Font Awesome CSS
import '@fortawesome/fontawesome-svg-core/styles.css'

const app = createApp(App)
const pinia = createPinia()
const head = createHead()

app.use(pinia)
app.use(router)
app.use(i18n)
app.use(fontawesome)
app.use(head)
// setupIcons(app)
setupDirectives(app)

// // 初始化用户状态
// const userStore = useUserStore()
// userStore.init()

// 挂载应用
app.mount('#app')

// 初始化主题（必须在应用挂载后调用）
const { initTheme } = useTheme()
initTheme()

// 初始化AOS动画
AOS.init({
  duration: 800,
  easing: 'ease-out-cubic',
  once: true,
  offset: 50,
  delay: 100
})

