import {defineStore} from 'pinia'
import {ref, reactive} from 'vue'
import {language} from '@/conf'
import type {Lang} from '@/types/lang'

export const useLanguageStore = defineStore('language', () => {
  const lang = ref<string>(language.default)
  const langPackage = ref<Lang[]>([])
  const setLang = (language: string) => {
    lang.value = language
  }
  const setLangPackage = (languages: Lang[]) => {
    langPackage.value = languages
  }

  function initLangPackage () {
    Object.keys(language.package).forEach((langId: any) => {
      const langCode = language.package[langId]
      langPackage.value.push({
        id: langId,
        value: langCode,
        label: language.alias[langCode]
      })
    })
    console.log(langPackage)
    // langPackage.value.sort((a: any, b: any) => language.order[a.value] - language.order[b.value])
  }

  return {lang, setLang, langPackage, setLangPackage, initLangPackage}
})
