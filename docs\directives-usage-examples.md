# Vue 3 指令使用示例

## observe-el-height 指令使用

### 基本用法

```vue
<template lang="pug">
div
  // 方式1: 使用 Vue 事件监听（推荐）
  .content-area(
    v-observe-el-height="'contentHeightChange'"
    @contentHeightChange="handleHeightChange"
  )
    p 这是一些动态内容...
    
  // 方式2: 使用 DOM 事件监听
  .another-area(
    v-observe-el-height="'sizeChange'"
    ref="areaRef"
  )
    p 更多内容...
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const areaRef = ref<HTMLElement>()

// 方式1: Vue 事件处理（推荐）
function handleHeightChange(data: { height: string; element: HTMLElement }) {
  console.log('高度变化:', data.height)
  console.log('元素:', data.element)
}

// 方式2: DOM 事件处理
onMounted(() => {
  if (areaRef.value) {
    areaRef.value.addEventListener('sizeChange', (event: CustomEvent) => {
      console.log('DOM事件 - 高度变化:', event.detail.height)
    })
  }
})
</script>
```

## Vue 2 vs Vue 3 事件对比

### Vue 2 (旧方式)
```javascript
// 在指令中
vnode.context.$emit('heightChange', data)

// 在组件中监听
<div v-observe-el-height @heightChange="handleChange"></div>
```

### Vue 3 (新方式)
```typescript
// 在指令中 - 双重兼容方案
// 1. Vue 事件
if (instance && typeof (instance as any).$emit === 'function') {
  (instance as any).$emit(eventName, data)
}
// 2. DOM 事件
el.dispatchEvent(new CustomEvent(eventName, { detail: data, bubbles: true }))

// 在组件中监听 - 两种方式都可以
// 方式1: Vue 事件 (推荐)
<div v-observe-el-height="'heightChange'" @heightChange="handleChange"></div>

// 方式2: DOM 事件
const el = ref()
onMounted(() => {
  el.value.addEventListener('heightChange', handleChange)
})
```

## 优势对比

| 特性 | Vue 事件 ($emit) | DOM 事件 (CustomEvent) |
|------|------------------|------------------------|
| 语法简洁 | ✅ `@eventName` | ❌ 需要 `addEventListener` |
| 类型安全 | ✅ TypeScript 支持 | ⚠️ 需要类型断言 |
| 组件树传播 | ✅ 遵循 Vue 事件系统 | ❌ 仅 DOM 事件冒泡 |
| 性能 | ✅ Vue 优化 | ⚠️ 原生 DOM 事件 |
| 调试 | ✅ Vue DevTools | ❌ 浏览器 DevTools |

## 推荐使用方式

1. **优先使用 Vue 事件**：在模板中用 `@eventName` 监听
2. **DOM 事件作为后备**：确保在各种场景下都能工作
3. **保持向后兼容**：现有代码无需修改

## 其他指令示例

### avatar 指令
```vue
<template lang="pug">
img(
  v-avatar="userInfo"
  @avatarError="handleAvatarError"
  @avatarLoaded="handleAvatarLoaded"
)
</template>

<script setup lang="ts">
const userInfo = {
  avatar: 'https://example.com/avatar.jpg',
  name: '张三'
}

function handleAvatarError() {
  console.log('头像加载失败，显示默认头像')
}

function handleAvatarLoaded() {
  console.log('头像加载成功')
}
</script>
```

### countdown 指令
```vue
<template lang="pug">
span(
  v-countdown="countdownConfig"
  @countdownFinish="handleCountdownFinish"
  @countdownTick="handleCountdownTick"
)
</template>

<script setup lang="ts">
const countdownConfig = {
  endTime: new Date('2024-12-31 23:59:59').getTime(),
  format: 'HH:mm:ss'
}

function handleCountdownFinish() {
  console.log('倒计时结束！')
}

function handleCountdownTick(data: { timeLeft: number, formatted: string }) {
  console.log('倒计时更新:', data.formatted)
}
</script>
``` 