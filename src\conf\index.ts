import storage from '@/commons/storage'

let shortUrlDefault = 'https://s.szdev.fleetup.cc'
if (/(online\.|staging\.)/.test(window.location.host)) {
  shortUrlDefault = 'https://eta.fleetup.cc'
} else {
  shortUrlDefault = 'https://s.szdev.fleetup.cc'
}
export const sApis = {
  ENDPOINTDEFAULT:
    (storage.userBaseInfo &&
      storage.userBaseInfo.endpoints &&
      storage.userBaseInfo.endpoints.default) ||
    window.location.origin,
  ADMIN:
    (storage.userBaseInfo &&
      storage.userBaseInfo.endpoints &&
      (storage.userBaseInfo.endpoints.admin || storage.userBaseInfo.endpoints.default)) ||
    window.location.origin,
  SHIPMENT:
    (storage.userBaseInfo &&
      storage.userBaseInfo.endpoints &&
      (storage.userBaseInfo.endpoints.shipment || storage.userBaseInfo.endpoints.default)) ||
    window.location.origin,
  SHORTURL:
    (storage.userBaseInfo &&
      storage.userBaseInfo.endpoints &&
      storage.userBaseInfo.endpoints.shortLink) ||
    shortUrlDefault
}
// Http interface address (type) root path
export const API = {
  // 开发环境
  development: {
    DEFAULT: '/api',
    NONE: '',
    LOCAL: import.meta.env.BASE_URL,
    ADMIN: sApis.ADMIN,
    ENDPOINTDEFAULT: sApis.ENDPOINTDEFAULT,
    SHIPMENT: sApis.SHIPMENT,
    CDN: 'https://cdn.fleetup.net',
    BAYANAT: 'https://maps.bayanat.co.ae',
    SHORTURL: '/szdev'
  },
  // Production environment
  production: {
    DEFAULT: window.location.origin,
    LOCAL: import.meta.env.BASE_URL,
    ADMIN: sApis.ADMIN,
    ENDPOINTDEFAULT: sApis.ENDPOINTDEFAULT,
    SHIPMENT: sApis.SHIPMENT, // test5.sz.fleetup.net：sApis.SHIPMENT, s3: window.location.origin
    CDN: 'https://cdn.fleetup.net',
    BAYANAT: 'https://maps.bayanat.co.ae',
    SHORTURL: sApis.SHORTURL
  }
}

// Project language configuration
export const language: Record<string, any> = {
  // Default Language
  default: 'en_US',
  // Support language '1': 'en_US', '2': 'es_ES', '3': 'pt_PT', '5': 'ar_AE'
  package: { '1': 'en_US', '2': 'es_ES', '3': 'pt_PT', '5': 'ar_AE' },
  order: {},
  // Language alias
  alias: {
    ar_AE: 'عربي',
    de_DE: 'Dansk',
    en_US: 'English',
    es_ES: 'Español',
    fr_FR: 'Français',
    ja_JP: '日本語',
    ko_KR: '대한민국 - 한국어',
    ru_RU: 'русский язык',
    pt_PT: 'Português'
  },
  // Language Pack Icon
  icons: {
    ar_AE: '/res/ar_AE.png',
    de_DE: '/res/de_DE.png',
    en_US: '/res/en_US.png',
    es_ES: '/res/es_ES.png',
    fr_FR: '/res/fr_FR.png',
    ja_JP: '/res/ja_JP.png',
    ko_KR: '/res/ko_KR.png',
    ru_RU: '/res/ru_RU.png',
    pt_PT: '/res/pt_PT.png'
  },
  // Language Pack Data
  data: {
    ar_AE: 'ar_AE.json',
    de_DE: 'de_DE.json',
    en_US: 'en_US.json',
    es_ES: 'es_ES.json',
    fr_FR: 'fr_FR.json',
    ja_JP: 'ja_JP.json',
    ko_KR: 'ko_KR.json',
    ru_RU: 'ru_RU.json',
    pt_PT: 'pt_PT.json'
  }
}

//User contact information
export const contactInfo: Record<string, any> = {
  careersEmail: '<EMAIL>',
  salesEmail: '<EMAIL>',
  address: 'app.common.footer.address',
  salesPhone: '13424389902',
  salesPhoneCountryCode: '+86',
  recruitUrl: 'https://job.carnoc.com/company?id=44ef8754c5ab6d23bdd190c069495d95'
}

export const filePrefix = {
  default: 'https://s3.us-west-2.amazonaws.com/uploads.szdev.fleetup.cc/',
  local: import.meta.env.BASE_URL,
  none: ''
}
