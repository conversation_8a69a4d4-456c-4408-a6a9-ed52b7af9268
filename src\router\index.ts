import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useSeo } from '@/composables/useSeo'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { setDocumentTitle } from '@/i18n'
import storage from '@/commons/storage'
import { useCommonStore } from '@/stores/common'

// Helper function to safely get meta properties
const getMetaProperty = (meta: any, key: string): string | undefined => {
  return meta && typeof meta[key] === 'string' ? meta[key] : undefined
}

// 导入所有路由模块
const modules = import.meta.glob('./modules/*.ts', { eager: true })

const routes: RouteRecordRaw[] = [
  // 路由将由各模块添加
]

// 遍历模块并注册路由
Object.values(modules).forEach((module: any) => {
  if (module.default) {
    module.default(routes)
  }
})
// 404 - Vue Router 4 syntax for catch-all routes
routes.push({
  path: '/:pathMatch(.*)*',
  redirect: '/404'
})
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 合并父子路由的 meta 信息
function getMergedMeta (to: any) {
  let merged = { ...to.meta }

  // 如果有父路由，合并父路由的 meta
  if (to.matched.length > 1) {
    const parentMeta = to.matched[0].meta
    merged = {
      ...parentMeta,  // 父路由的 meta 作为基础
      ...to.meta,     // 子路由的 meta 可以覆盖父路由
      // 特殊处理面包屑，如果子路由有自己的面包屑，使用子路由的
      breadcrumb: to.meta.breadcrumb || parentMeta.breadcrumb
    }
  }

  return merged
}

// 全局前置守卫
router.beforeEach((to, from, next) => {

// loading
  NProgress.start()
  // setting document title
  let docTitleI18n: string | undefined
  const docTitleI18nModule = getMetaProperty(to.meta, 'docTitleI18nModule') || (to.matched && to.matched[0] && getMetaProperty(to.matched[0].meta, 'docTitleI18nModule'))
  // priority: docTitleI18n > i18n > default
  if (docTitleI18nModule) docTitleI18n = `${getMetaProperty(to.meta, 'docTitleI18n') || getMetaProperty(to.meta, 'i18n') || 'default'}`

  // Use Pinia store instead of Vuex
  const commonStore = useCommonStore()
  commonStore.setDocumentTitle({
    docTitleI18n,
    docTitleI18nModule,
    title: getMetaProperty(to.meta, 'title'),
    default: 'FleetUp AI'
  })
  if (!to.matched.some(r => r.meta.ignoreAuth)) {
    if (storage.xAuthToken) {
      // 判断权限
      next()
    } else {
      next({
        name: 'login'
      })
    }
  } else {
    next()
  }
  // 设置文档标题
  // document.title = to.meta.title
  //   ? `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE || 'Tech Blog'}`
  //   : import.meta.env.VITE_APP_TITLE || 'Tech Blog'
  // 认证检查
  // const isLoggedIn = !!localStorage.getItem('token')

  // // 需要认证的路由
  // if (!to.meta.ignoreAuth && !isLoggedIn) {
  //   next({ name: 'login', query: { redirect: to.fullPath } })
  // }
  // // 仅游客可访问的路由
  // else if (to.meta.guest && isLoggedIn) {
  //   next({ name: 'home' })
  // }
  // else {
  //   next()
  // }
})
router.beforeResolve(async (to, from, next) => {
  // Get the current document title state from Pinia store
  const commonStore = useCommonStore()
  setDocumentTitle(commonStore.documentTitle)
  next()
})
router.afterEach(route => {
  // delete loading
  NProgress.done()
})

export default router
