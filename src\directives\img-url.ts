import type { Directive, DirectiveBinding } from 'vue'
import { API, filePrefix } from '@/conf'
import defaultImg from '@/assets/images/placeholder/img-none.png'

interface ImgUrlBinding extends DirectiveBinding {
  value: string
}

const imgUrl: Directive = {
  mounted(el: HTMLImageElement, binding: ImgUrlBinding) {
    updateImage(el, binding)
  },
  
  updated(el: HTMLImageElement, binding: ImgUrlBinding) {
    if (binding.oldValue !== binding.value) {
      updateImage(el, binding)
    }
  }
}

function updateImage(el: HTMLImageElement, binding: ImgUrlBinding) {
  const prefixType = el.getAttribute('prefix') || 'default'
  const isLocal = el.getAttribute('type') === 'local'
  const imgURL = isLocal ? binding.value : (filePrefix[prefixType as keyof typeof filePrefix] || '') + binding.value
  const defaultURL = el.getAttribute('src') || defaultImg
  
  // Set default image
  el.setAttribute('src', defaultURL)
  
  if (binding.value) {
    // If image URL exists, try to load it
    const img = new Image()
    img.onload = () => {
      el.setAttribute('src', imgURL)
    }
    img.onerror = () => {
      el.setAttribute('src', defaultURL)
      console.warn('Failed to load image:', imgURL)
    }
    img.src = imgURL
  } else {
    el.setAttribute('src', defaultURL)
    console.log('No image URL provided, using default')
  }
}

export default {
  'img-url': imgUrl
} 