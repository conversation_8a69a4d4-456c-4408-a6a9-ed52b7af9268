import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, RegisterForm, UpdateProfileForm } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)

  // Login status
  const isLoggedIn = computed(() => !!token.value)

  // User information
  const userProfile = computed(() => user.value)

  // Login
  async function login(form: LoginForm) {
    try {
      // TODO: Call login API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(form)
      })

      if (!response.ok) {
        throw new Error('Login failed')
      }

      const data = await response.json()
      token.value = data.token
      user.value = data.user
      localStorage.setItem('token', data.token)
      return true
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }

  // Register
  async function register(form: RegisterForm) {
    try {
      // TODO: Call register API
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(form)
      })

      if (!response.ok) {
        throw new Error('Registration failed')
      }

      const data = await response.json()
      token.value = data.token
      user.value = data.user
      localStorage.setItem('token', data.token)
      return true
    } catch (error) {
      console.error('Registration error:', error)
      return false
    }
  }

  // Get user information
  async function getProfile() {
    if (!token.value) {
      return null
    }

    try {
      // TODO: Call get user info API
      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to get user info')
      }

      const data = await response.json()
      user.value = data
      return data
    } catch (error) {
      console.error('Get user info error:', error)
      return null
    }
  }

  // Update user information
  async function updateProfile(form: UpdateProfileForm) {
    if (!token.value) {
      return false
    }

    try {
      // TODO: Call update user info API
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token.value}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(form)
      })

      if (!response.ok) {
        throw new Error('Failed to update user info')
      }

      const data = await response.json()
      user.value = data
      return true
    } catch (error) {
      console.error('Update user info error:', error)
      return false
    }
  }

  // Logout
  function logout() {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
  }

  // Initialize
  function init() {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      token.value = savedToken
      getProfile().catch(() => {
        logout()
      })
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    userProfile,
    login,
    register,
    getProfile,
    updateProfile,
    logout,
    init
  }
}) 