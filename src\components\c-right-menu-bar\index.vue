<template lang="pug">
  .right-menu-bar
    .item.user-operate
      el-dropdown
        .user-avatar
          //img(v-img-url="userInfo.info.avatar" type="local")
          img(v-img-url="defaultAvatar" prefix="none")
        template(#dropdown)
          el-dropdown-menu
            el-dropdown-item(disabled) {{appVersion}}
            el-dropdown-item(disabled) {{$t('app.ui.hi', { name: userInfo.userName || userInfo.sub })}}
            // el-dropdown-item(divided @click="handleMy('profileSettings')") {{$t('app.chat.setting')}}
            el-dropdown-item(@click="handleLoginOut") {{$t('app.login.logout')}}
    .item.data-version
      el-dropdown(@command="versionChange")
        i.iconfont.icon-data-conversion
        template(#dropdown)
          el-dropdown-menu
            el-dropdown-item(disabled) {{$t('app.chat.databaseVersion')}}
            el-dropdown-item.right-menu-bar_version-item(v-for="item in databaseVersionList" :class="{'active':chatDatasetVersion===item.version}" :key="`${item.version}(${item.description})`" :command="item.version")
              template
                .version {{item.version}}
                .description {{item.description}}
    //-.item.item-action.popover
      el-dropdown(@command="changeLanguage", trigger="click")
        .current-language-icon
          img(v-img-url="languageIcon" prefix="local")
        template(#dropdown v-if="languageConfig.package")
          el-dropdown-menu
            el-dropdown-item(v-for="item in languageConfig.package" :key="item" :command="item", :disabled="language === item") {{languageConfig.alias[item]}}
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCommonStore } from '@/stores/common'
import { useChatStore } from '@/stores/chat'
import { useLanguageStore } from '@/stores/language'
import { useLanguage } from '@/composables/useLanguage'

// Keep original imports as comments for reference
// import * as TYPES_COMMON from '@/stores/common/types'
// import * as TYPES_CHAT from '@/stores/chat/types'
// import MxLanguage from '@/mixins/language'
// import { getDatabaseVersionList } from '@/commons/api'

const { t } = useI18n()

// Pinia stores
const commonStore = useCommonStore()
const chatStore = useChatStore()
const languageStore = useLanguageStore()

// Use language composable (replaces MxLanguage mixin)
const {
  languageIcon,
  languageAlias,
  language,
  languageConfig,
  changeLanguage
} = useLanguage()

// Reactive data
const defaultAvatar = ref('/res/avatar.gif')
const databaseVersionList = ref<any[]>([])

// Computed properties from stores
const userInfo = computed(() => commonStore.userBaseInfo)
const appVersion = computed(() => chatStore.appVersion)
const chatDatasetVersion = computed(() => chatStore.chatDatasetVersion)

// Methods
const handleLoginOut = async () => {
  try {
    // Use Pinia store action instead of Vuex mutation
    commonStore.logout()
    window.location.replace('/#/login')
  } catch (error) {
    console.error('Logout error:', error)
  }
}

const initVersionList = async () => {
  try {
    // Use Pinia store action instead of Vuex action
    await chatStore.getAppVersion()
    
    // Keep original API call as comment for future implementation
    // const res = await getDatabaseVersionList(this)
    const res: any[] = [] // Mock response for now
    
    if (res && res.length) {
      databaseVersionList.value = res
    } else {
      databaseVersionList.value = []
    }
  } catch (error) {
    console.error('Failed to get database version list:', error)
    databaseVersionList.value = []
  }
}

const versionChange = (value: string) => {
  if (value === chatDatasetVersion.value) return
  // Use Pinia store action instead of Vuex mutation
  chatStore.setDatabaseVersion(value)
}

// Keep original method signature for profile settings (commented out in template)
// const handleMy = (action: string) => {
//   console.log('Handle my action:', action)
// }

// Lifecycle hooks
onMounted(() => {
  initVersionList()
})
</script>

<style lang="scss">
.right-menu-bar_version-item {
  padding: 5px 20px;
  line-height: 1.5em;
  max-width: 300px;
  word-break: break-word;
  
  .version {
    color: #545353;
  }
  
  .description {
    padding: 3px 3px 0;
    color: #c3c3c3;
    font-size: 12px;
  }
  
  &.active {
    background: #ebebeb;
    
    .version {
      font-weight: bold;
      color: #002E5D;
    }
    
    .description {
      color: #545353;
    }
  }
}
</style>
<style lang="scss" scoped>
@use './index.scss';
</style>
