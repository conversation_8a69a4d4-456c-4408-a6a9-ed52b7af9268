import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useChatStore } from '@/stores/chat'
import { askQuestions, saveMessage } from '../api'
import { getLanguageInfo } from '@/i18n'
import moment from 'moment'

// Utility functions (to be moved to utils later)
function uuid2 (): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
}

function resizeObserver (callback: ResizeObserverCallback): ResizeObserver {
  return new ResizeObserver(callback)
}

function formatDateTime (format: string): string {
  const now = new Date()
  const year = now.getUTCFullYear()
  const month = String(now.getUTCMonth() + 1).padStart(2, '0')
  const day = String(now.getUTCDate()).padStart(2, '0')
  const hours = String(now.getUTCHours()).padStart(2, '0')
  const minutes = String(now.getUTCMinutes()).padStart(2, '0')
  const seconds = String(now.getUTCSeconds()).padStart(2, '0')

  // Match the original Vue 2 format: moment.utc().format(`${timeFormat} HH:mm:ss`)
  return `${format} ${hours}:${minutes}:${seconds}`.replace('YYYY-MM-DD', `${year}-${month}-${day}`)
}

export interface SendMessageOptions {
  isRegenerate?: boolean
  customMsg?: any
}

export interface MessageParams {
  id: string
  createTime: string
  requestText: string
  answerIndex: number
  allMessages: any[]
}

export function useSendMessage () {
  const { t } = useI18n()
  const chatStore = useChatStore()

  // Reactive state
  const message = ref('')
  const sendMsgLoading = ref(false)
  const currentAnswerId = ref('')
  const currentMessageId = ref('')
  const currentMessage = ref<any>(null)
  const showRegenerate = ref(false)
  const inRegenerate = ref(false)
  const emptySendMsgLoading = ref(false)

  // Computed properties
  const chatDatasetVersion = computed(() => chatStore.chatDatasetVersion)

  // Methods
  const handleKeyCode = (event: KeyboardEvent, sendMsgFn: () => void) => {
    if (event.keyCode === 13 && !event.shiftKey) {
      event.preventDefault()
      sendMsgFn()
    }
  }

  const emptySendMsg = (emit: (event: string) => void) => {
    if (emptySendMsgLoading.value) {
      ElMessage.warning(t('app.chat.sendMsgFrequently'))
      return
    }
    emptySendMsgLoading.value = true
    emit('createChat')
  }

  const getMessageFromStorage = (msgId: string, chatData: any, currentChat: string) => {
    const chatDetail = chatData[currentChat]?.chatDetail
    return chatDetail?.find((item: any) => item.id === msgId)
  }

  const removeResizeObserver = ({ answerId, observer }: { answerId: string; observer: ResizeObserver }) => {
    const el = document.querySelector(`.answer_${answerId}`)
    if (observer && el) {
      observer.unobserve(el)
    }
  }

  const observerScrollBottom = ({ chatId, answerId, currentChat, scrollRef }: {
    chatId: string
    answerId: string
    currentChat: string
    scrollRef?: any
  }) => {
    const observer = resizeObserver(() => {
      if (answerId === currentAnswerId.value) {
        scrollRef?.scrollToBottom()
      }
    })

    nextTick(() => {
      if (chatId === currentChat) {
        const el = document.querySelector(`.answer_${answerId}`)
        if (el) {
          observer.observe(el)
        }
      }
    })

    return { observer }
  }

  const typeAnswer = ({ msg, chatId, answerIndex, quesionIndex, currentChat, chatDetail }: {
    msg: string
    chatId: string
    answerIndex: number
    quesionIndex: number
    currentChat: string
    chatDetail: any[]
  }) => {
    if (chatId === currentChat) {
      chatDetail[quesionIndex].allMessages[answerIndex].responseText += msg
    }
  }

  const toSaveMessage = async ({
    data,
    msgId,
    answerId,
    question,
    chatId,
    isRegenerate,
    elapsedTime,
    cancelSourceList
  }: {
    data: any
    msgId: string
    answerId: string
    question: string
    chatId: string
    isRegenerate?: boolean
    elapsedTime?: number | null
    cancelSourceList?: any
  }) => {
    if (!answerId) {
      throw new Error('answerId is required')
    }

    const idPrefix = isRegenerate ? 'regenerate_' : ''
    const saveCancelId = `${idPrefix}save_${answerId}`
    let completion = ''
    let responseText = data && (data.responseText || data.msg)

    if (responseText) {
      responseText = data.responseText = responseText.replace(/^\n/, '')
      completion = responseText
    }

    elapsedTime = elapsedTime || 0
    const params = {
      id: answerId,
      prompt: question,
      completion,
      elapsedTime: elapsedTime.toFixed(2),
      datasetVersion: chatDatasetVersion.value
    }

    if (isRegenerate) {
      (params as any).referId = msgId
    }

    await saveMessage(params, { id: chatId }, {
      cancelSource: cancelSourceList,
      cancelSourceId: saveCancelId
    })
  }

  const sendMsg = async (opts: SendMessageOptions = {}, context: {
    currentChat: string
    chatDetail: any[]
    userInfo: any
    language: string
    timeFormat: string
    fetchAbortController: AbortController
    cancelSourceList: any
    scrollRef?: any
    sendMessageBoxRef?: any
  }) => {
    const { isRegenerate, customMsg } = opts
    const {
      currentChat,
      chatDetail,
      userInfo,
      language,
      timeFormat,
      fetchAbortController,
      cancelSourceList,
      scrollRef,
      sendMessageBoxRef
    } = context

    let resizeObs: ResizeObserver | null = null
    const question = isRegenerate ? customMsg.requestText : message.value

    if (question === '' || sendMsgLoading.value) return false

    sendMsgLoading.value = true
    currentAnswerId.value = uuid2()

    const idPrefix = isRegenerate ? 'regenerate_' : ''
    const askCancelId = `${idPrefix}ask_${currentAnswerId.value}`
    const saveCancelId = `${idPrefix}save_${currentAnswerId.value}`

    let askRes: any
    let params: MessageParams

    if (isRegenerate) {
      currentMessageId.value = customMsg.id
      params = customMsg
    } else {
      currentMessageId.value = uuid2()
      params = {
        id: currentMessageId.value,
        createTime: moment.utc().format(`${timeFormat} HH:mm:ss`),
        requestText: question,
        answerIndex: 0,
        allMessages: []
      }
    }

    try {
      // Use proper language utility like in Vue 2 version
      const langInfo = getLanguageInfo(language, 'en_US')
      const requestParams = {
        ds: chatDatasetVersion.value,
        tid: currentChat,
        username: userInfo.userName,
        lang: langInfo.short,
        word: question,
        createTime: null
      }

      // Push the question
      if (!isRegenerate) {
        chatDetail.push(params)
      }

      currentMessage.value = params
      sendMessageBoxRef?.setMsg('')

      // Push the answer
      params.allMessages.push({
        id: currentAnswerId.value,
        createTime: moment.utc().format(`${timeFormat} HH:mm:ss`),
        responseText: '',
        flag: null,
        flagLoading: false
      })

      params.answerIndex = params.allMessages.length - 1
      scrollRef?.scrollToBottom()

      const { observer } = observerScrollBottom({
        chatId: currentChat,
        answerId: currentAnswerId.value,
        currentChat,
        scrollRef
      })
      resizeObs = observer

      // Ask questions
      const startTime = performance.now()
      const chatId = currentChat

      askRes = await askQuestions(requestParams, {
        cb: (data: any) => typeAnswer({
          ...data,
          currentChat,
          chatDetail
        }),
        index: params.allMessages.length - 1,
        quesionIndex: chatDetail.length - 1,
        signal: fetchAbortController.signal
      })

      const endTime = performance.now()
      const elapsedTime = (endTime - startTime) / 1000

      // Save message
      await toSaveMessage({
        data: askRes,
        msgId: currentMessageId.value,
        answerId: currentAnswerId.value,
        question,
        chatId,
        isRegenerate,
        elapsedTime,
        cancelSourceList
      })
      requestParams.createTime = moment.utc().format(`${timeFormat} HH:mm:ss`)
      // // push the answer
      // params.allMessages.push({
      //   id: this.currentAnswerId,
      //   createTime: moment.utc().format(`${this.timeFormat} HH:mm:ss`),
      //   responseText: askRes.responseText,
      //   flag: null,
      //   flagLoading: false
      // })
      if (!showRegenerate.value) {
        showRegenerate.value = true
      }
      // !isRegenerate && this.updateLocalStorage()
      if (resizeObs) {
        removeResizeObserver({ answerId: currentAnswerId.value, observer: resizeObs })
      }
      return params

    } catch (e) {
      console.error(e)

      if (resizeObs) {
        removeResizeObserver({ answerId: currentAnswerId.value, observer: resizeObs })
      }
      const lastAnswer = params.allMessages && params.allMessages[params.allMessages.length - 1]
      const isNotCancel = (e && (e as any).name !== 'AbortError' && !(e as any).__CANCEL__)

      if (isNotCancel) {
        if (lastAnswer && !lastAnswer.responseText) {
          params.allMessages.pop()
          params.answerIndex = params.allMessages.length - 1
        }

        if (lastAnswer && lastAnswer.responseText) {
          askRes = askRes || { responseText: lastAnswer.responseText }
          await toSaveMessage({
            data: askRes,
            msgId: currentMessageId.value,
            answerId: currentAnswerId.value,
            question,
            chatId: currentChat,
            isRegenerate,
            elapsedTime: null,
            cancelSourceList
          })
        }

        if (!showRegenerate.value) {
          showRegenerate.value = true
        }
        // this.updateLocalStorage()
      }
    } finally {
      sendMsgLoading.value = false
      if (cancelSourceList[askCancelId]) delete cancelSourceList[askCancelId]
      if (cancelSourceList[saveCancelId]) delete cancelSourceList[saveCancelId]
    }
  }

  const regenerate = async (context: Parameters<typeof sendMsg>[1]) => {
    try {
      if (!context.chatDetail || !context.chatDetail.length) return false

      const message = context.chatDetail[context.chatDetail.length - 1]
      console.log(message)

      if (!message) return false

      inRegenerate.value = true
      const newMessage = await sendMsg({ isRegenerate: true, customMsg: message }, context)

      if (!newMessage) return false

      sendMsgLoading.value = true
      // context.chatDetail.pop() // pop old message
      // context.chatDetail.push(newMessage) // re-add new message
      // this.updateLocalStorage()
    } catch (e) {
      console.error(e)
    } finally {
      sendMsgLoading.value = false
      inRegenerate.value = false
    }
  }

  const saveMessageByCancelTheRequest = async ({
    answerRes,
    chatDetail,
    message,
    answerId,
    chatId,
    isRegenerate,
    cancelSourceList
  }: {
    answerRes: any
    chatDetail: any[]
    message: any
    answerId: string
    chatId: string
    isRegenerate?: boolean
    cancelSourceList?: any
  }) => {
    await toSaveMessage({
      data: answerRes,
      msgId: message.id,
      answerId,
      question: message.requestText,
      chatId,
      isRegenerate,
      elapsedTime: null,
      cancelSourceList
    })

    if (!showRegenerate.value) {
      showRegenerate.value = true
    }
    // this.updateChatData({ id: chatId, data: JSON.parse(JSON.stringify(chatDetail)) })
  }

  return {
    // State
    message,
    sendMsgLoading,
    currentAnswerId,
    currentMessageId,
    currentMessage,
    showRegenerate,
    inRegenerate,
    emptySendMsgLoading,

    // Computed
    chatDatasetVersion,

    // Methods
    handleKeyCode,
    emptySendMsg,
    getMessageFromStorage,
    sendMsg,
    removeResizeObserver,
    observerScrollBottom,
    typeAnswer,
    toSaveMessage,
    regenerate,
    saveMessageByCancelTheRequest
  }
}
