import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig, CancelTokenSource } from 'axios'
import jsCookie from 'js-cookie'
import { ElLoading, ElMessage } from 'element-plus'
import type { LoadingInstance } from 'element-plus/es/components/loading/src/loading'
import storage from '@/commons/storage'

// API配置
interface ApiConfig {
  DEFAULT: string;
  TRUNK: string;
  SZDEV: string;
  [key: string]: string;
}

const API_CONFIG: {
  development: ApiConfig;
  production: ApiConfig;
} = {
  development: {
    LOCAL: import.meta.env.BASE_URL,
    DEFAULT: '/api',
    TRUNK: '/trunk',
    SZDEV: '/szdev'
  },
  production: {
    LOCAL: import.meta.env.BASE_URL,
    DEFAULT: '/api',
    TRUNK: '/trunk',
    SZDEV: '/szdev'
  }
}

// 环境变量
const isProd = import.meta.env.PROD
const BASE_PATH = isProd ? API_CONFIG.production : API_CONFIG.development

// HTTP状态信息接口
interface HttpStatus {
  [key: number]: string;
  DEFAULT_ERROR: string;
  [key: string]: string;
}

// 注意：这里优先使用storage中的状态信息，如果不存在则使用默认值
const HTTP_STATUS: HttpStatus = storage.httpStatus || {
  400: '请求错误',
  401: '未授权，请重新登录',
  403: '拒绝访问',
  404: '请求地址出错',
  408: '请求超时',
  500: '服务器内部错误',
  501: '服务未实现',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时',
  505: 'HTTP版本不受支持',
  DEFAULT_ERROR: '系统异常，请稍后重试'
}

// 扩展AxiosRequestConfig接口
export interface ExtendedRequestConfig extends AxiosRequestConfig {
  mask?: boolean;
  apiType?: string;
  token?: boolean;
  formData?: boolean;
  toast?: boolean;
  successToast?: boolean;
  cancelSource?: CancelTokenSource[] | Record<string, CancelTokenSource>;
  cancelSourceId?: string | number;
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  // baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 60000, // 1分钟超时
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// 全局loading实例
let gloading: LoadingInstance | null = null

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const extConfig = config as unknown as ExtendedRequestConfig

    // 显示加载遮罩
    if (extConfig.mask) {
      gloading = ElLoading.service()
      delete extConfig.mask
    }

    // 处理headers
    const headers = (extConfig.headers = extConfig.headers || {})

    // 处理formData
    if (extConfig.method === 'post' || extConfig.method === 'delete') {
      if (extConfig.formData) {
        const formData = new FormData()
        if (extConfig.data) {
          Object.keys(extConfig.data).forEach(key => {
            formData.append(key, extConfig.data[key])
          })
        }
        extConfig.data = formData
        delete extConfig.formData
        delete headers['Content-Type'] // 让浏览器自动设置Content-Type和boundary
      }
    } else {
      headers['Accept'] = 'application/json; charset=UTF-8, text/javascript, */*; q=0.01'
    }

    // 设置token
    if (extConfig.token !== false) {
      const token = localStorage.getItem('token') || storage.xAuthToken
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      } else {
        // 兼容旧系统，使用JSESSIONID
        const jsessionid = jsCookie.get('JSESSIONID')
        if (jsessionid) {
          headers['Authorization'] = `JSESSIONID ${jsessionid}`
        }
      }
      delete extConfig.token
    }
    // 处理API路径
    if (extConfig.apiType && extConfig.apiType === 'NONE') {
      // 不添加任何前缀
    } else if (extConfig.url) {
      const apiType = extConfig.apiType || 'DEFAULT'
      const basePath = apiType in BASE_PATH ? BASE_PATH[apiType] : BASE_PATH.DEFAULT
      extConfig.url = basePath + extConfig.url
    }

    // 处理请求取消
    registerCancelSource(extConfig)

    return config
  },
  (error) => {
    // 关闭加载遮罩
    if (gloading) {
      gloading.close()
      gloading = null
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 关闭加载遮罩
    if (gloading) {
      gloading.close()
      gloading = null
    }

    const extConfig = response.config as unknown as ExtendedRequestConfig
    const res = response.data

    // 处理业务错误
    if (res && res.success === false) {
      const errMessage = res.errorMessage || res.error_message || res.message || res.msg || res.info || '未知错误'

      if (extConfig.toast !== false && extConfig.successToast !== false) {
        ElMessage.error(errMessage)
      }
    }

    // 根据自定义错误码判断请求是否成功
    if (res.code && res.code !== 200 && res.code !== 0) {
      // 处理业务错误
      console.error('业务错误：', res.message || '未知错误')

      // 例如：401未授权，可能是token过期
      if (res.code === 401 || res.code === 1003) {
        // 处理登录过期的情况
        localStorage.removeItem('token')
        jsCookie.remove('JSESSIONID')
        window.location.href = '/login'
      }

      if (extConfig.toast !== false) {
        ElMessage.error(res.message || '未知错误')
      }

      return Promise.reject(new Error(res.message || '未知错误'))
    } else {
      return res
    }
  },
  (error) => {
    // 关闭加载遮罩
    if (gloading) {
      gloading.close()
      gloading = null
    }

    // 判断是否是取消请求
    if (axios.isCancel(error)) {
      console.info('请求已取消:', error.message)
      return Promise.reject(error)
    }

    // 处理HTTP错误
    if (error.response) {
      const { response } = error
      const extConfig = error.config as unknown as ExtendedRequestConfig
      const data = response.data
      const status = response.status

      console.error(error.config.url, status, JSON.stringify(data))

      let errMessage = data.errorMessage || data.message || data.msg || HTTP_STATUS[status as number] || HTTP_STATUS.DEFAULT_ERROR

      switch (status) {
        case 401:
        case 403:
          errMessage = '您的登录已过期或无权限，请重新登录'
          localStorage.removeItem('token')
          jsCookie.remove('JSESSIONID')

          if (isProd) {
            const url = error.request?.responseURL || ''
            const isSameHost = url.indexOf(window.location.host) !== -1
            isSameHost && (window.location.href = '/login')
          }
          break

        case 404:
        case 500:
          const code = +data.code || ''
          switch (code) {
            case 1003:
              errMessage = '您还未登录或登录已失效'
              break
            default:
              if (!isProd) {
                errMessage = `${errMessage} - 状态码：${code || status}`
              }
              break
          }
          break
      }

      // 显示错误提示
      if (extConfig.toast !== false) {
        ElMessage.error(errMessage)
      }
    } else {
      // 网络错误或超时
      ElMessage.error('网络错误，请检查您的网络连接')
    }

    return Promise.reject(error)
  }
)

// 注册取消请求的token
function registerCancelSource(config: ExtendedRequestConfig): void {
  if (!config.cancelSource) {
    return
  }

  const CancelToken = axios.CancelToken
  const source = CancelToken.source()

  if (Array.isArray(config.cancelSource)) {
    config.cancelSource.push(source)
    config.cancelToken = source.token
  } else if (typeof config.cancelSource === 'object') {
    const id = config.cancelSourceId || new Date().getTime()
    config.cancelSource[id] = source
    config.cancelToken = source.token
  }
}

// 封装GET请求
export function get<T>(url: string, params?: any, config?: ExtendedRequestConfig): Promise<T> {
  return service.get(url, { params, ...config })
}

// 封装POST请求
export function post<T>(url: string, data?: any, config?: ExtendedRequestConfig): Promise<T> {
  return service.post(url, data, config)
}

// 封装PUT请求
export function put<T>(url: string, data?: any, config?: ExtendedRequestConfig): Promise<T> {
  return service.put(url, data, config)
}

// 封装DELETE请求
export function del<T>(url: string, config?: ExtendedRequestConfig): Promise<T> {
  return service.delete(url, config)
}

// 创建取消令牌
export function createCancelToken(): { source: CancelTokenSource; token: any } {
  const source = axios.CancelToken.source()
  return {
    source,
    token: source.token
  }
}

// 多接口并发请求
export async function multiRequest<T>(requests: Record<string, [string, ExtendedRequestConfig]>): Promise<Record<string, T>> {
  const requestArr: Promise<any>[] = []
  const keys: string[] = []

  for (const key in requests) {
    const [url, options] = requests[key]
    keys.push(key)
    requestArr.push(service(url, options))
  }

  try {
    const results = await Promise.all(requestArr)
    const resultMap: Record<string, T> = {}

    results.forEach((result, index) => {
      resultMap[keys[index]] = result
    })

    return resultMap
  } catch (error) {
    console.error('多接口请求错误:', error)
    return Promise.reject(error)
  }
}

// 导出axios实例
export default service
